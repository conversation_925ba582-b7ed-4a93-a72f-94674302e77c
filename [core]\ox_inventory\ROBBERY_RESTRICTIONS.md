# Robbery Restrictions System

This system allows you to prevent certain items from being stolen/robbed from other players in the ox_inventory system.

## Configuration

The robbery restrictions are configured in `data/robbery_restrictions.lua`. This file contains:

### Protected Items List
Add item names to the `protected_items` array to prevent them from being stolen:

```lua
protected_items = {
    'id_card',
    'driver_license',
    'phone',
    'radio',
    -- Add more items here
}
```

### Settings
Configure notification behavior and messages:

```lua
settings = {
    notify_robber = true,  -- Notify the person trying to steal
    notify_victim = false, -- Notify the person being robbed
    messages = {
        robber_blocked = "You cannot steal this item.",
        victim_protected = "Someone tried to steal your %s but it was protected.",
    }
}
```

## Admin Commands

### `/addprotecteditem <item_name>`
Adds an item to the protected list dynamically.
- **Example**: `/addprotecteditem phone`
- **Permission**: Admin only

### `/removeprotecteditem <item_name>`
Removes an item from the protected list.
- **Example**: `/removeprotecteditem phone`
- **Permission**: Admin only

### `/listprotecteditems`
Lists all currently protected items.
- **Permission**: Admin only

## Export Functions

Other resources can interact with the robbery restrictions system using these exports:

### `addProtectedItem(itemName)`
```lua
-- Add an item to the protected list
local success = exports.ox_inventory:addProtectedItem('special_key')
```

### `removeProtectedItem(itemName)`
```lua
-- Remove an item from the protected list
local success = exports.ox_inventory:removeProtectedItem('special_key')
```

### `isItemProtected(itemName)`
```lua
-- Check if an item is protected
local isProtected = exports.ox_inventory:isItemProtected('phone')
```

### `getProtectedItems()`
```lua
-- Get all protected items
local protectedItems = exports.ox_inventory:getProtectedItems()
```

## How It Works

1. When a player tries to take an item from another player's inventory (robbery scenario)
2. The system checks if the item is in the protected items list
3. If protected, the action is blocked and notifications are sent (if enabled)
4. The robber receives an error message
5. Optionally, the victim can be notified of the attempted theft

## Common Protected Items

Consider protecting these types of items:
- **Identity Items**: ID cards, licenses, passports
- **Communication**: Phones, radios
- **Keys**: Car keys, house keys, access cards
- **Important Documents**: Contracts, certificates
- **Personal Items**: Items that shouldn't be lost in robberies

## Notes

- The system only prevents robbery (taking items from other players)
- It does not affect normal trading, giving items, or admin commands
- Items can still be dropped, sold, or used normally
- The protection only applies during player-to-player inventory interactions where one player is taking from another without permission
