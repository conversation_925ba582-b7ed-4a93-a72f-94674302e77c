-- Test script for robbery restrictions system
-- This file is for testing purposes only and should not be included in production

if not IsDuplicityVersion() then return end -- Server-side only

-- Test function to verify the robbery restrictions system
local function testRobberyRestrictions()
    print("^2[Robbery Restrictions Test] Starting tests...^0")
    
    -- Test 1: Check if configuration loads correctly
    local restrictions = lib.load('data.robbery_restrictions')
    if restrictions and restrictions.protected_items then
        print("^2[Test 1] ✓ Configuration loaded successfully^0")
        print(("^3[Test 1] Found %d protected items^0"):format(#restrictions.protected_items))
    else
        print("^1[Test 1] ✗ Failed to load configuration^0")
        return
    end
    
    -- Test 2: Check export functions
    local testItem = 'test_protected_item'
    
    -- Test adding a protected item
    local addSuccess = exports.ox_inventory:addProtectedItem(testItem)
    if addSuccess then
        print("^2[Test 2a] ✓ Successfully added protected item^0")
    else
        print("^1[Test 2a] ✗ Failed to add protected item^0")
    end
    
    -- Test checking if item is protected
    local isProtected = exports.ox_inventory:isItemProtected(testItem)
    if isProtected then
        print("^2[Test 2b] ✓ Item correctly identified as protected^0")
    else
        print("^1[Test 2b] ✗ Item not identified as protected^0")
    end
    
    -- Test removing a protected item
    local removeSuccess = exports.ox_inventory:removeProtectedItem(testItem)
    if removeSuccess then
        print("^2[Test 2c] ✓ Successfully removed protected item^0")
    else
        print("^1[Test 2c] ✗ Failed to remove protected item^0")
    end
    
    -- Test 3: Check if item is no longer protected
    local isStillProtected = exports.ox_inventory:isItemProtected(testItem)
    if not isStillProtected then
        print("^2[Test 3] ✓ Item correctly identified as no longer protected^0")
    else
        print("^1[Test 3] ✗ Item still showing as protected after removal^0")
    end
    
    -- Test 4: Get all protected items
    local allProtected = exports.ox_inventory:getProtectedItems()
    if allProtected and type(allProtected) == 'table' then
        print("^2[Test 4] ✓ Successfully retrieved protected items list^0")
        print(("^3[Test 4] Total protected items: %d^0"):format(#allProtected))
    else
        print("^1[Test 4] ✗ Failed to retrieve protected items list^0")
    end
    
    print("^2[Robbery Restrictions Test] Tests completed!^0")
end

-- Test command for admins
lib.addCommand('testrobberyrestrictions', {
    help = 'Test the robbery restrictions system',
    params = {},
    restricted = 'group.admin',
}, function(source)
    testRobberyRestrictions()
    TriggerClientEvent('ox_lib:notify', source, { type = 'info', description = 'Robbery restrictions test completed. Check server console for results.' })
end)

-- Auto-run test on resource start (comment out for production)
CreateThread(function()
    Wait(5000) -- Wait for everything to load
    testRobberyRestrictions()
end)
