return {
    Weapons = {

        ['WEAPON_SHOE'] = {
            label = 'Shoe',
            weight = 5,
            throwable = true,
        },

        ['WEAPON_HACKINGDEVICE'] = {
    		label = 'Plant Scanner',
    		weight = 1200,
		},

		['WEAPON_PXDS9'] = {
            label = 'PINK XDS9',
            weight = 1500,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo'
        },

		['WEAPON_PEPPERSPRAY'] = {
			label = 'Pepperspray',
			weight = 500,
			durability = 0,
		},



        --- NoobSlothy
         ['WEAPON_AK47'] 		 	    = { label = 'AK-47', 		    weight = 1000,	durability = 0.18,	ammoname = 'xd_rifle_ammo',},
         ['WEAPON_DE'] 					= { label = 'Desert Eagle',	    weight = 1000,	durability = 0.09,	ammoname = 'xd_pistol_ammo',},
         ['WEAPON_FNX45'] 			    = { label = 'FN FNX-45', 	    weight = 1000,	durability = 0.09,	ammoname = 'xd_pistol_ammo',},
         ['WEAPON_GLOCK17'] 		    = { label = ' Glock 17',        weight = 1000,	durability = 0.09,	ammoname = 'xd_pistol_ammo',},
         ['WEAPON_M4'] 				    = { label = ' M4A1', 		    weight = 1000,	durability = 0.09,	ammoname = 'xd_gov_rifle_ammo',},
         ['WEAPON_M9'] 			        = { label = 'Beretta M9A3',     weight = 1000,	durability = 0.09,	ammoname = 'xd_pistol_ammo',},
         ['WEAPON_M70'] 				= { label = 'M70', 			    weight = 1000,	durability = 0.09,	ammoname = 'xd_rifle_ammo',},
         ['WEAPON_M1911'] 			    = { label = 'M1911', 		    weight = 1000,	durability = 0.09,	ammoname = 'xd_pistol_ammo',},
         ['WEAPON_UZI'] 			    = { label = 'UZI', 			    weight = 1000,	durability = 0.18,	ammoname = 'xd_smg_ammo',},
         ['WEAPON_MAC10'] 				= { label = 'MAC-10', 		    weight = 1000,	durability = 0.09,	ammoname = 'xd_smg_ammo',},
         ['WEAPON_MOSSBERG'] 	        = { label = 'Mossberg 500',     weight = 1000,	durability = 0.20,	ammoname = 'xd_shotgun_ammo',},
         ['WEAPON_REMINGTON'] 			= { label = 'Remington 870',    weight = 1000,	durability = 0.05,	ammoname = 'xd_shotgun_ammo',},
         ['WEAPON_SCARH'] 		        = { label = 'PD SCAR-H', 	    weight = 1000,	durability = 0.09,	ammoname = 'xd_gov_rifle_ammo',},
         ['WEAPON_AR15'] 			    = { label = 'PD AR-15', 	    weight = 1000,	durability = 0.09,	ammoname = 'xd_rifle_ammo',},
         ['WEAPON_MK14'] 			    = { label = 'PD MK14', 		    weight = 1000,	durability = 0.09,	ammoname = 'xd_sniper_ammo',},
         ['WEAPON_HUNTINGRIFLE'] 		= { label = 'Hunting Rifle',    weight = 1000,	durability = 0.09,	ammoname = 'xd_sniper_ammo',},
         ['WEAPON_MP9'] 			    = { label = 'MP9', 				weight = 1000,	durability = 0.09,	ammoname = 'xd_smg_ammo',},
         ['WEAPON_M110'] 		        = { label = 'M110', 			weight = 1000,	durability = 0.09,	ammoname = 'xd_sniper_ammo',},
         ['WEAPON_HK416'] 		        = { label = 'HK-416', 			weight = 1000,	durability = 0.09,	ammoname = 'xd_rifle_ammo',},
         ['WEAPON_AK74'] 		 	    = { label = 'AK-74', 		    weight = 1000,	durability = 0.18,	ammoname = 'xd_rifle_ammo',},
         ['WEAPON_AKS74'] 		 	    = { label = 'AK-S74', 		    weight = 1000,	durability = 0.09,	ammoname = 'xd_rifle_ammo',},
         ['WEAPON_GLOCK18C'] 	        = { label = 'Glock 18C', 	    weight = 1000,	durability = 0.09,	ammoname = 'xd_pistol_ammo',},
         ['WEAPON_GLOCK22'] 			= { label = 'Glock 22',         weight = 1000,	durability = 0.09,	ammoname = 'xd_pistol_ammo',},
         ['WEAPON_MP5'] 				= { label = 'H&K MP5', 		    weight = 1000,	durability = 0.09,	ammoname = 'xd_smg_ammo',},
         ['WEAPON_COLBATON'] 			= { label = 'PD Baton',         weight = 1000,	durability = 0.09},
         ['WEAPON_KATANA'] 			    = { label = 'Katana', 		    weight = 1000,	durability = 0.09,},
         ['WEAPON_SLEDGEHAMMER'] 	    = { label = 'Sledge Hammer',    weight = 1000,	durability = 0.09,},
         ['WEAPON_DREWJDILDO'] 	        = { label = 'DREWJDILDO',       weight = 1000,	durability = 0.09,},
         ['WEAPON_FUTRSCYTHE'] 	        = { label = 'FUTRSCYTHE',       weight = 1000,	durability = 0.09,},
         ['WEAPON_FUTRHAMMER'] 		    = { label = 'FUTRHAMMER', 		weight = 1000,	durability = 0.09,},
         ['WEAPON_PANDASCYTHE'] 		= { label = 'Sky Scythe', 		weight = 1000,	durability = 0.09,},
         ['WEAPON_SHIV'] 				= { label = 'Shiv', 		    weight = 1000,	durability = 0.09,}, 


        --Kyros Weapon Pack V6
		['WEAPON_BLUEARP'] = {
			label = 'BLUE ARP',
			weight = 4500,
			durability = 0.1,
			ammoname = 'xd_rifle_ammo'
		},

		['WEAPON_DAR15'] = {
			label = 'DESERT AR-15',
			weight = 4900,
			durability = 0.1,
			ammoname = 'xd_rifle_ammo'
		},

		['WEAPON_M16'] = {
			label = 'M16 RIFLE',
			weight = 5900,
			durability = 0.1,
			ammoname = 'xd_rifle_ammo'
		},

		['WEAPON_SPEAR'] = {
			label = 'MCX SPEAR',
			weight = 5200,
			durability = 0.1,
			ammoname = 'xd_rifle_ammo'
		},

		['WEAPON_MK47'] = {
			label = 'MK47',
			weight = 5200,
			durability = 0.1,
			ammoname = 'xd_rifle_ammo'
		},

		['WEAPON_RAM7K'] = {
			label = 'RAM-7 KNIGHT',
			weight = 5700,
			durability = 0.1,
			ammoname = 'xd_rifle_ammo'
		},

		['WEAPON_REDAUG'] = {
			label = 'RED AUG BURST',
			weight = 4600,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_CZBREN'] = {
			label = 'CZ BREN',
			weight = 5300,
			durability = 0.1,
			ammoname = 'xd_rifle_ammo'
		},

		['WEAPON_TP9SF'] = {
			label = 'CANIK TP9SF',
			weight = 1700,
			durability = 0.5,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_G19XD'] = {
			label = 'G19X DESERT',
			weight = 2700,
			durability = 0.5,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_G17B'] = {
			label = 'GLOCK 17 BLACKOUT',
			weight = 1900,
			durability = 0.5,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_GLOCK18'] = {
			label = 'GLOCK 18',
			weight = 1500,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_GLOCK20S'] = {
			label = 'GLOCK 20 SWITCH',
			weight = 1600,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_GLOCK22S'] = {
			label = 'GLOCK 22 SWITCH',
			weight = 1600,
			durability = 0.5,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_GLOCK45'] = {
			label = 'GLOCK 45',
			weight = 1700,
			durability = 0.5,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_GLOCKDEMON'] = {
			label = 'GLOCK DEMON',
			weight = 1600,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_M9A3'] = {
			label = 'GREY M9A3',
			weight = 1900,
			durability = 0.5,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_ILLGLOCK19X'] = {
			label = 'ILLEGAL G19X SWITCH',
			weight = 2100,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_SR9'] = {
			label = 'RUGER SR9',
			weight = 1700,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_GX4'] = {
			label = 'TAURUS GX4',
			weight = 1200,
			durability = 0.5,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_TGLOCK45'] = {
			label = 'TAN GLOCK 45',
			weight = 1800,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_SUB2000'] = {
			label = 'KEL-TEC SUB2000',
			weight = 4000,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_MAC4A1'] = {
			label = 'MAC-4A1',
			weight = 3000,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_MICROMP5'] = {
			label = 'MICRO MP5',
			weight = 2900,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_MINIAK47'] = {
			label = 'MINI AK-47',
			weight = 4800,
			durability = 0.1,
			ammoname = 'xd_rifle_ammo'
		},

		['WEAPON_RAM9D'] = {
			label = 'RAM-9 DESERT',
			weight = 5900,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_UDP9'] = {
			label = 'UDP-9',
			weight = 2850,
			durability = 0.1,
			ammoname = 'xd_pistol_ammo'
		},

		['WEAPON_BERETTA1301'] = {
			label = 'BERETTA 1301',
			weight = 4600,
			durability = 0.1,
			ammoname = 'xd_shotgun_ammo'
		},

		['WEAPON_MINISHOTGUN'] = {
			label = 'MINI SHOTTY',
			weight = 2900,
			durability = 0.1,
			ammoname = 'xd_shotgun_ammo'
		},


        ---- F9 Weapons
        ['WEAPON_NEVA'] = {
			label = 'NEVA',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

        ['WEAPON_M416P'] = {
			label = 'M416P',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

        ['WEAPON_M133'] = {
			label = 'M-133',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

        ['WEAPON_JRBAK'] = {
			label = 'JRB-AK',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

        ['WEAPON_GALILAR'] = {
			label = 'GALIL AR',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

        ['WEAPON_FAMASU1'] = {
			label = 'Famas Underground',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

        ['WEAPON_FN42'] = {
			label = 'FN-42',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

        ['WEAPON_DRH'] = {
			label = 'DRH',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

        ['WEAPON_CFS'] = {
			label = 'CFS',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},


        -- New F9 (July)

        ['WEAPON_L85_CHRISTMAS'] = {
			label = 'L85 CHRISTMAS',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

		['WEAPON_NVRIFLE_PURPLE'] = {
			label = 'NV Rifle Purple',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

		['WEAPON_M270D'] = {
			label = 'M270D',
			weight = 1000,
			durability = 0.09,
			ammoname = 'xd_pistol_ammo',
		},

		['WEAPON_P250_ASIIMOV'] = {
			label = 'P250 ASIIMOV',
			weight = 1000,
			durability = 0.09,
			ammoname = 'xd_pistol_ammo',
		},

		['WEAPON_VECTOR'] = {
			label = 'VECTOR',
			weight = 2500,
			durability = 0.09,
			ammoname = 'xd_smg_ammo',
		},

		-- NEW F9s (JUNE)
		['WEAPON_M4_TACTICAL_RED'] = {
			label = 'M4 Tactical Red',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

		['WEAPON_GRAUV2'] = {
			label = 'GRAU V2',
			weight = 5000,
			durability = 0.09,
			ammoname = 'xd_rifle_ammo',
		},

		['WEAPON_HFSMGV2'] = {
			label = 'HFSMG V2',
			weight = 2500,
			durability = 0.09,
			ammoname = 'xd_smg_ammo',
		},

		['WEAPON_DESERT_EAGLE'] = {
			label = 'DESERT EAGLE V2',
			weight = 1000,
			durability = 0.09,
			ammoname = 'xd_pistol_ammo',
		},

		['WEAPON_M27S'] = {
			label = 'M27S',
			weight = 1000,
			durability = 0.09,
			ammoname = 'xd_pistol_ammo',
		},

        -- Katanas
        ['WEAPON_KATANA_R'] = {
            label = 'Red Katana',
            weight = 1000,
            durability = 0.00,
        },
        ['WEAPON_KATANA_Y'] = {
            label = 'Yellow Katana',
            weight = 1000,
            durability = 0.00,
        },
        ['WEAPON_KATANA_B'] = {
            label = 'Blue Katana',
            weight = 1000,
            durability = 0.00,
        },
        ['WEAPON_KATANA_G'] = {
            label = 'Green Katana',
            weight = 1000,
            durability = 0.00,
        },

        ['WEAPON_DIGISCANNER'] = {
            label = 'Signal booster',
            weight = 1200,
        },

        --Kyros Weapon Pack POLICE
        ['WEAPON_PDBATON'] = {
            label = 'PD BATON',
            weight = 1000,
            durability = 0.15,
        },

        ['WEAPON_PDM700'] = {
            label = 'PD REMINGTON M700',
            weight = 9400,
            durability = 0.15,
            ammoname = 'xd_gov_sniper_ammo'
        },

        ['WEAPON_BTASER'] = {
            label = 'PD Black Taser',
            weight = 300,
            durability = 0.15,
        },

        ['WEAPON_YTASER'] = {
            label = 'PD Yellow Taser',
            weight = 300,
            durability = 0.15,
        },

        ['WEAPON_PDG19'] = {
            label = 'PD GLOCK 19G4',
            weight = 1800,
            durability = 0.15,
            ammoname = 'xd_gov_pistol_ammo'
        },

        ['WEAPON_HK417'] = {
            label = 'PD HK417',
            weight = 4400,
            durability = 0.15,
            ammoname = 'xd_gov_rifle_ammo'
        },

        ['WEAPON_PDCM607'] = {
            label = 'PD COLT M607',
            weight = 4500,
            durability = 0.15,
            ammoname = 'xd_gov_rifle_ammo'
        },

        ['WEAPON_PDC7'] = {
            label = 'PD COLT C7',
            weight = 4500,
            durability = 0.15,
            ammoname = 'xd_gov_rifle_ammo'
        },

        ['WEAPON_PDG22'] = {
            label = 'PD GLOCK 22',
            weight = 4500,
            durability = 0.15,
            ammoname = 'xd_gov_pistol_ammo'
        },

        ['WEAPON_PDBM3'] = {
            label = 'PD BENELLI M3',
            weight = 3500,
            durability = 0.15,
            ammoname = 'xd_gov_shotgun_ammo'
        },

        ['WEAPON_PDHK33'] = {
            label = 'PD HK433',
            weight = 3600,
            durability = 0.15,
            ammoname = 'xd_gov_rifle_ammo'
        },

        ['WEAPON_ADVANCEDRIFLE'] = {
            label = 'Advanced Rifle',
            weight = 3180,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO',
        },

        ['WEAPON_APPISTOL'] = {
            label = 'AP Pistol',
            weight = 1220,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_ASSAULTRIFLE'] = {
            label = 'Assault Rifle',
            weight = 3470,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO',
        },

        ['WEAPON_ASSAULTRIFLE_MK2'] = {
            label = 'Assault Rifle MK2',
            weight = 3300,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO',
        },

        ['WEAPON_ASSAULTSHOTGUN'] = {
            label = 'Assault Shotgun',
            weight = 3100,
            durability = 0.05,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge',
        },

        ['WEAPON_ASSAULTSMG'] = {
            label = 'Assault SMG',
            weight = 2850,
            durability = 0.05,
            ammoname = 'xd_smg_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_BALL'] = {
            label = 'Ball',
            weight = 149,
            throwable = true,
        },

        -- ['WEAPON_BAT'] = {
        --     label = 'Bat',
        --     weight = 1134,
        --     durability = 0.1,
        -- },

        -- ['WEAPON_BATTLEAXE'] = {
        --     label = 'Battle Axe',
        --     weight = 1200,
        --     durability = 0.1,
        -- },

        -- ['WEAPON_BOTTLE'] = {
        --     label = 'Bottle',
        --     weight = 350,
        --     durability = 0.1,
        -- },

        ['WEAPON_BULLPUPRIFLE'] = {
            label = 'Bullpup Rifle',
            weight = 2900,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.8x42mm DBP87',
        },

        ['WEAPON_BULLPUPRIFLE_MK2'] = {
            label = 'Bullpup Rifle MK2',
            weight = 2900,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.8x42mm DBP87',
        },

        ['WEAPON_BULLPUPSHOTGUN'] = {
            label = 'Bullpup Shotgun',
            weight = 3100,
            durability = 0.2,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge'
        },

        ['WEAPON_BZGAS'] = {
            label = 'BZ Gas',
            weight = 600,
            throwable = true,
            caliber = 'Grenade Pin'
        },

        ['WEAPON_CARBINERIFLE'] = {
            label = 'Carbine Rifle',
            weight = 3100,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO'
        },

        ['WEAPON_CARBINERIFLE_MK2'] = {
            label = 'Carbine Rifle MK2',
            weight = 3000,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO'
        },

        ['WEAPON_CERAMICPISTOL'] = {
            label = 'Ceramic Pistol',
            weight = 700,
            durability = 0.2,
            ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_COMBATMG'] = {
            label = 'Combat MG',
            weight = 10000,
            durability = 0.02,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO'
        },

        ['WEAPON_COMBATMG_MK2'] = {
            label = 'Combat MG MK2',
            weight = 10500,
            durability = 0.02,
            ammoname = 'xd_rifle_ammo',
            caliber = '7.62x51mm NATO',
        },

        ['WEAPON_COMBATPDW'] = {
            label = 'Combat PDW',
            weight = 2700,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_COMBATPISTOL'] = {
            label = 'Combat Pistol',
            weight = 970,
            durability = 0.2,
            ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_COMBATSHOTGUN'] = {
            label = 'Combat Shotgun',
            weight = 4400,
            durability = 0.2,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge',
        },

        ['WEAPON_COMPACTRIFLE'] = {
            label = 'Compact Rifle',
            weight = 2700,
            durability = 0.05,
            ammoname = 'xd_rifle_ammo',
            caliber = '7.62x39mm Soviet',
        },

        -- ['WEAPON_CROWBAR'] = {
        --     label = 'Crowbar',
        --     weight = 2500,
        --     durability = 0.1,
        -- },

        ['WEAPON_DAGGER'] = {
            label = 'Dagger',
            weight = 800,
            durability = 0.1,
        },

        ['WEAPON_DBSHOTGUN'] = {
            label = 'Double Barrel Shotgun',
            weight = 3175,
            durability = 0.4,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge',
        },

        ['WEAPON_DOUBLEACTION'] = {
            label = 'Double Action Revolver',
            weight = 940,
            durability = 0.2,
            ammoname = 'xd_pistol_ammo',
            caliber = '.38 Long Colt',
        },

        ['WEAPON_FIREEXTINGUISHER'] = {
            label = 'Fire Extinguisher',
            weight = 1500,
        },

        ['WEAPON_FIREWORK'] = {
            label = 'Firework Launcher',
            weight = 1000,
            durability = 0.5,
            caliber = 'Firework Packaging',
        },

        ['WEAPON_FLARE'] = {
            label = 'Flare',
            weight = 235,
            throwable = true,
            caliber = 'Flare Cap',
        },

        ['WEAPON_FLASHLIGHT'] = {
            label = 'Flashlight',
            weight = 125,
            durability = 0.1,
        },

        ['WEAPON_GOLFCLUB'] = {
            label = 'Golf Club',
            weight = 330,
            durability = 0.1,
        },

        ['WEAPON_GUSENBERG'] = {
            label = 'Gusenberg',
            weight = 4900,
            durability = 0.04,
            ammoname = 'xd_smg_ammo',
            caliber = '.45 ACP',
        },

        -- ['WEAPON_HAMMER'] = {
        --     label = 'Hammer',
        --     weight = 1200,
        --     durability = 0.1,
        -- },

        ['WEAPON_HATCHET'] = {
            label = 'Dildo',
            weight = 1000,
            durability = 0.1,
        },

        ['WEAPON_HEAVYRIFLE'] = {
            label = 'Heavy Rifle',
            weight = 2750,
            durability = 0.2,
            ammoname = 'xd_rifle_ammo',
            caliber = '7.62x51mm NATO',
        },

        ['WEAPON_HAZARDCAN'] = {
            label = 'Hazard Can',
            weight = 12000,
        },

        ['WEAPON_METALDETECTOR'] = {
            label = 'Metal Detector',
            weight = 1200,
        },

        ['WEAPON_FERTILIZERCAN'] = {
            label = 'Fertilizer Can',
            weight = 12000,
        },

        ['WEAPON_HEAVYPISTOL'] = {
            label = 'Heavy Pistol',
            weight = 1100,
            durability = 0.2,
            ammoname = 'xd_pistol_ammo',
            caliber = '.45 ACP',
        },

        ['WEAPON_HEAVYSHOTGUN'] = {
            label = 'Heavy Shotgun',
            weight = 3600,
            durability = 0.1,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge',
        },

        -- ['WEAPON_KNIFE'] = {
        --     label = 'Knife',
        --     weight = 300,
        --     durability = 0.1,
        -- },

        ['WEAPON_KNUCKLE'] = {
            label = 'Knuckle Dusters',
            weight = 300,
            durability = 0.1,
        },

        ['WEAPON_MACHETE'] = {
            label = 'Machete',
            weight = 1000,
            durability = 0.1,
        },

        ['WEAPON_MACHINEPISTOL'] = {
            label = 'Machine Pistol',
            weight = 1400,
            durability = 0.05,
            ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_MARKSMANPISTOL'] = {
            label = 'Marksman Pistol',
            weight = 1588,
            durability = 0.5,
            ammoname = 'xd-ammo-22',
            caliber = '.45-70 Government',
        },

        ['WEAPON_MARKSMANRIFLE'] = {
            label = 'Marksman Rifle',
            weight = 7500,
            durability = 0.4,
            ammoname = 'xd_sniper_ammo',
            caliber = '7.76x51mm NATO',
        },

        ['WEAPON_MG'] = {
            label = 'Machine Gun',
            weight = 9000,
            durability = 0.02,
            ammoname = 'xd_rifle_ammo',
            caliber = '7.76x51mm NATO',
        },

        ['WEAPON_MICROSMG'] = {
            label = 'Micro SMG',
            weight = 4000,
            durability = 0.1,
            ammoname = 'xd_smg_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_MILITARYRIFLE'] = {
            label = 'Military Rifle',
            weight = 3600,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO',
        },

        ['WEAPON_MINISMG'] = {
            label = 'Mini SMG',
            weight = 2770,
            durability = 0.05,
            ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_MOLOTOV'] = {
            label = 'Molotov',
            weight = 1800,
            throwable = true,
        },

        ['WEAPON_MUSKET'] = {
            label = 'Musket',
            weight = 4500,
            durability = 0.5,
            ammoname = 'xd-ammo-musket',
            caliber = 'A Percussion Cap?',
        },

        ['WEAPON_NAVYREVOLVER'] = {
            label = 'Navy Revolver',
            weight = 2000,
            durability = 0.2,
            ammoname = 'xd_pistol_ammo',
            caliber = '.38 Centerfire',
        },

        ['WEAPON_NIGHTSTICK'] = {
            label = 'Nightstick',
            weight = 1000,
            durability = 0.1,
        },

        ['WEAPON_PETROLCAN'] = {
            label = 'Gas Can',
            weight = 0,
        },

        ['WEAPON_PISTOL'] = {
            label = 'Walther P99',
            weight = 970,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_PISTOL50'] = {
            label = 'PD Desert Eagle',
            weight = 2000,
            durability = 0.1,
            ammoname = 'xd_gov_pistol_ammo',
            caliber = '.50 Action Express',
        },

        ['WEAPON_PISTOL_MK2'] = {
            label = 'Pistol MK2',
            weight = 970,
            durability = 0.5,
            ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_POOLCUE'] = {
            label = 'Pool Cue',
            weight = 146,
            durability = 0.1,
        },

        ['WEAPON_PUMPSHOTGUN'] = {
            label = 'Pump Shotgun',
            weight = 3400,
            durability = 0.1,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge',
        },

        ['WEAPON_SMG'] = {
			label = 'PD SMG',
			weight = 2500,
			durability = 0.175,
			ammoname = 'xd_gov_smg_ammo',
            caliber = '9x19mm Parabellum'
		},

        ['WEAPON_PUMPSHOTGUN_MK2'] = {
            label = 'Pump Shotgun MK2',
            weight = 3200,
            durability = 0.1,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge',
        },

        ['WEAPON_REVOLVER'] = {
            label = 'Revolver',
            weight = 2260,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo',
            caliber = '.357 Magnum',
        },

        ['WEAPON_REVOLVER_MK2'] = {
            label = 'Revolver MK2',
            weight = 1500,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo',
            caliber = '.44 Magnum',
        },

        ['WEAPON_SAWNOFFSHOTGUN'] = {
            label = 'Sawn Off Shotgun',
            weight = 2380,
            durability = 0.1,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge',
        },


        ['WEAPON_SMG_MK2'] = {
            label = 'SMG Mk2',
            weight = 2700,
            durability = 0.05,
            ammoname = 'xd_smg_ammo',
            caliber = '9x19mm Parabellum',
        },

        ['WEAPON_SMOKEGRENADE'] = {
            label = 'Smoke Grenade',
            weight = 600,
            throwable = true,
            caliber = 'Grenade Pin',
        },

        ['WEAPON_SNOWBALL'] = {
            label = 'Snow Ball',
            weight = 5,
            throwable = true,
        },

        ['WEAPON_SNSPISTOL'] = {
            label = 'SNS Pistol',
            weight = 465,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo',
            caliber = '.40 S&W',
        },

        ['WEAPON_SNSPISTOL_MK2'] = {
            label = 'SNS Pistol MK2',
            weight = 465,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo',
            caliber = '.40 S&W',
        },

        ['WEAPON_SPECIALCARBINE'] = {
            label = 'Special Carbine',
            weight = 3000,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '.40 S&W',
        },

        ['WEAPON_SPECIALCARBINE_MK2'] = {
            label = 'Special Carbine MK2',
            weight = 3370,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO',
        },

        ['WEAPON_STONE_HATCHET'] = {
            label = 'Stone Hatchet',
            weight = 800,
            durability = 0.1,
        },

        ['WEAPON_STUNGUN'] = {
            label = 'Tazer',
            weight = 227,
            durability = 0.1,
            caliber = 'Taser AFID',
        },

        ['WEAPON_AUTOSHOTGUN'] = {
            label = 'Sweeper Shotgun',
            weight = 4400,
            durability = 0.05,
            ammoname = 'xd_shotgun_ammo',
            caliber = '12 Gauge',
        },

        ['WEAPON_SWITCHBLADE'] = {
            label = 'Switch Blade',
            weight = 300,
            durability = 0.1,
            anim = { 'anim@melee@switchblade@holster', 'unholster', 200, 'anim@melee@switchblade@holster', 'holster', 600 },
        },

        ['WEAPON_VINTAGEPISTOL'] = {
            label = 'Vintage Pistol',
            weight = 100,
            durability = 0.1,
            ammoname = 'xd_pistol_ammo',
            caliber = '.32 ACP',
        },

        -- ['WEAPON_WRENCH'] = {
        --     label = 'Wrench',
        --     weight = 2500,
        --     durability = 0.1,
        -- },

        ['WEAPON_PRECISIONRIFLE'] = {
            label = 'Precision Rifle',
            weight = 4800,
            durability = 0.4,
            ammoname = 'xd_sniper_ammo',
            caliber = '.223 Remington'
        },

        ['WEAPON_TACTICALRIFLE'] = {
            label = 'Tactical Rifle',
            weight = 3400,
            durability = 0.03,
            ammoname = 'xd_rifle_ammo',
            caliber = '5.56x45mm NATO'
        },

        -- DoItDigital Pistol
		['WEAPON_FN502'] = {
			label = 'FN502',
			weight = 3400,
			durability = 0.5,
			ammoname = 'xd_pistol_ammo',
            caliber = '9x19mm Parabellum'
		},
    },

    Components = {
        -- Kyro Pack
        ['at_clip_extended_pistol'] = {
			label = 'Extended Pistol Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_APPISTOL_CLIP_02`,
					`COMPONENT_CERAMICPISTOL_CLIP_02`,
					`COMPONENT_COMBATPISTOL_CLIP_02`,
					`COMPONENT_HEAVYPISTOL_CLIP_02`,
					`COMPONENT_PISTOL_CLIP_02`,
					`COMPONENT_PISTOL_MK2_CLIP_02`,
					`COMPONENT_PISTOL50_CLIP_02`,
					`COMPONENT_SNSPISTOL_CLIP_02`,
					`COMPONENT_SNSPISTOL_MK2_CLIP_02`,
					`COMPONENT_VINTAGEPISTOL_CLIP_02`,
                    `COMPONENT_TECPISTOL_CLIP_02`,
  					`COMPONENT_PINKGLOCK19_CLIP_02`, -- female
					`COMPONENT_PXDS9_CLIP_02`, -- female
                    `COMPONENT_P226_CLIP_02`, -- v1
                    `COMPONENT_G18C_CLIP_02`, -- v1
                    `COMPONENT_G17_CLIP_02`, -- v1
                    `COMPONENT_GARDONE_CLIP_02`, -- v1
					`COMPONENT_CJ_CLIP_02`, -- v2
					`COMPONENT_M45A1V2_CLIP_02`, -- v2
					`COMPONENT_B93R_CLIP_02`, -- v2
					`COMPONENT_MAKAROV_CLIP_02`, -- v2
					`COMPONENT_659_CLIP_02`, -- v2
                    `COMPONENT_G2C_CLIP_02`, -- v3
                    `COMPONENT_TGLOCK_CLIP_02`, -- v3
                    `COMPONENT_G26_CLIP_02`, -- v3
					`COMPONENT_ARPISTOL_BOXMAG`, -- v4
					`COMPONENT_GLOCK17_CLIP_02`, -- v4
					`COMPONENT_GLOCK18C_CLIP_02`, -- v4
					`COMPONENT_PDG19G4_CLIP_02`, -- pd
					`COMPONENT_BLUEGLOCKS_CLIP_02`, -- v5
					`COMPONENT_GLOCK41_CLIP_02`, -- v5
					`COMPONENT_GLOCKBEAMS_CLIP_02`, -- v5
					`COMPONENT_ILLGLOCK17_CLIP_02`, -- v5
					`COMPONENT_MGGLOCK_CLIP_02`, -- v5
					`COMPONENT_MIDASGLOCK_CLIP_02`, -- v5
					`COMPONENT_TGLOCK19_CLIP_02`, -- v5
					`COMPONENT_TP9SF_CLIP_EXTENDED`, -- v6
					`COMPONENT_G19XD_CLIP_EXTENDED`, -- v6
					`COMPONENT_G17B_CLIP_02`, -- v6
					`COMPONENT_GLOCK20S_CLIP_EXTENDED`, -- v6
					`COMPONENT_GLOCK22_CLIP_EXTENDED`, -- v6
					`COMPONENT_GLOCK22S_CLIP_EXTENDED`, -- v6
					`COMPONENT_GLOCK45_CLIP_EXTENDED`, -- v6
					`COMPONENT_GLOCKDEMON_CLIP_EXTENDED`, -- v6
					`COMPONENT_M9A3_CLIP_02`, -- v6
					`COMPONENT_ILLGLOCK19X_CLIP_EXTENDED`, -- v6
					`COMPONENT_SR9_CLIP_EXTENDED`, -- v6
					`COMPONENT_GX4_CLIP_EXTENDED`, -- v6
					`COMPONENT_TGLOCK45_CLIP_EXTENDED`, -- v6
                    `COMPONENT_TGLOCK19_CLIP_02`, -- v5
					`COMPONENT_CARTELGLOCK_CLIP_EXTENDED`, -- mafia [A]
					`COMPONENT_CRIPSGLOCK_CLIP_EXTENDED`, -- mafia [A]
					`COMPONENT_BLOODSGLOCK_CLIP_EXTENDED`, -- mafia [A]
					`COMPONENT_BMGLOCK_CLIP_EXTENDED`, -- mafia [A]
					`COMPONENT_FAMILIASGLOCK_CLIP_EXTENDED`, -- mafia [A]
					`COMPONENT_KINGZGLOCK_CLIP_EXTENDED`, -- mafia [B]
					`COMPONENT_GRAPEGLOCK_CLIP_EXTENDED`, -- mafia [B]
					`COMPONENT_HOOVERGLOCK_CLIP_EXTENDED`, -- mafia [B]
					`COMPONENT_MOBGLOCK_CLIP_EXTENDED`, -- mafia [B]
					`COMPONENT_PLAYBOYGLOCK_CLIP_EXTENDED`, -- mafia [B]
                    `w_pi_fn502_mag2`,
				},
				usetime = 2500
			}
		},

		['at_clip_clear'] = {
			label = 'Clear Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_TANARP_CLIP_04`, -- rifle
					`COMPONENT_LBTANARP_CLIP_04`, -- rifle
					`COMPONENT_WOARP_CLIP_04`, -- rifle
					`COMPONENT_ILLGLOCK17_CLIP_04`, -- pistol
					`COMPONENT_BLUEGLOCKS_CLIP_05`, -- pistol
					`COMPONENT_GLOCKBEAMS_CLIP_05`, -- pistol
					`COMPONENT_MGGLOCK_CLIP_05`, -- pistol
					`COMPONENT_TGLOCK19_CLIP_05`, -- pistol
					`COMPONENT_MIDASGLOCK_CLIP_05`, -- pistol
					`COMPONENT_REDARP_CLIP_04`, -- rifle
					`COMPONENT_BLUEARP_CLIP_CLEAR`, -- v6
					`COMPONENT_DAR15_CLIP_CLEAR`, -- v6
					`COMPONENT_G19XD_CLIP_CLEAR`, -- v6
					`COMPONENT_GLOCK20S_CLIP_CLEAR`, -- v6
					`COMPONENT_GLOCK22_CLIP_CLEAR`, -- v6
					`COMPONENT_GLOCK22S_CLIP_CLEAR`, -- v6
					`COMPONENT_GLOCK45_CLIP_CLEAR`, -- v6
					`COMPONENT_TGLOCK45_CLIP_CLEAR`, -- v6
					`COMPONENT_ILLGLOCK19X_CLIP_CLEAR`, -- v6
					`COMPONENT_GX4_CLIP_CLEAR`, -- v6
					`COMPONENT_UDP9_CLIP_EXTENDED`, -- v6
                    `COMPONENT_CARTELGLOCK_CLIP_CLEAR`, -- mafia [A]
					`COMPONENT_CRIPSGLOCK_CLIP_CLEAR`, -- mafia [A]
					`COMPONENT_BLOODSGLOCK_CLIP_CLEAR`, -- mafia [A]
					`COMPONENT_BMGLOCK_CLIP_CLEAR`, -- mafia [A]
					`COMPONENT_FAMILIASGLOCK_CLIP_CLEAR`, -- mafia [A]
					`COMPONENT_PLAYBOYGLOCK_CLIP_CLEAR`, -- mafia [B]
					`COMPONENT_MOBGLOCK_CLIP_CLEAR`, -- mafia [B]
					`COMPONENT_GRAPEGLOCK_CLIP_CLEAR`, -- mafia [B]
					`COMPONENT_HOOVERGLOCK_CLIP_CLEAR`, -- mafia [B]
					`COMPONENT_KINGZGLOCK_CLIP_CLEAR`, -- mafia [B]
				},
				usetime = 2500
			}
		},

		['at_clip_100_black'] = {
			label = 'BLK 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_CARTELGLOCK_CLIP_BLACK`, -- mafia
					`COMPONENT_CRIPSGLOCK_CLIP_BLACK`, -- mafia
					`COMPONENT_BLOODSGLOCK_CLIP_BLACK`, -- mafia
					`COMPONENT_BMGLOCK_CLIP_BLACK`, -- mafia
					`COMPONENT_FAMILIASGLOCK_CLIP_BLACK`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_100_red'] = {
			label = 'Red 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_CARTELGLOCK_CLIP_RED`, -- mafia
					`COMPONENT_BLOODSGLOCK_CLIP_RED`, -- mafia
					`COMPONENT_BMGLOCK_CLIP_RED`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_100_white'] = {
			label = 'WT 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_CARTELGLOCK_CLIP_WHITE`, -- mafia
					`COMPONENT_CRIPSGLOCK_CLIP_WHITE`, -- mafia
					`COMPONENT_BLOODSGLOCK_CLIP_WHITE`, -- mafia
					`COMPONENT_BMGLOCK_CLIP_WHITE`, -- mafia
					`COMPONENT_FAMILIASGLOCK_CLIP_WHITE`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_100_blue'] = {
			label = 'Blue 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_CRIPSGLOCK_CLIP_BLUE`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_100_green'] = {
			label = 'GRN 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_FAMILIASGLOCK_CLIP_GREEN`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_black'] = {
			label = 'BLK BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_BMARP_CLIP_BLACK`, -- mafia
					`COMPONENT_FAMILIASARP_CLIP_BLACK`, -- mafia
					`COMPONENT_CARTELARP_CLIP_BLACK`, -- mafia
					`COMPONENT_CRIPSARP_CLIP_BLACK`, -- mafia
					`COMPONENT_BLOODSARP_CLIP_BLACK`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_red'] = {
			label = 'RED BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_BMARP_CLIP_RED`, -- mafia
					`COMPONENT_FAMILIASARP_CLIP_RED`, -- mafia
					`COMPONENT_CARTELARP_CLIP_RED`, -- mafia
					`COMPONENT_CRIPSARP_CLIP_RED`, -- mafia
					`COMPONENT_BLOODSARP_CLIP_RED`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_blue'] = {
			label = 'BLUE BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_BMARP_CLIP_BLUE`, -- mafia
					`COMPONENT_FAMILIASARP_CLIP_BLUE`, -- mafia
					`COMPONENT_CARTELARP_CLIP_BLUE`, -- mafia
					`COMPONENT_CRIPSARP_CLIP_BLUE`, -- mafia
					`COMPONENT_BLOODSARP_CLIP_BLUE`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_green'] = {
			label = 'GRN BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_BMARP_CLIP_GREEN`, -- mafia
					`COMPONENT_FAMILIASARP_CLIP_GREEN`, -- mafia
					`COMPONENT_CARTELARP_CLIP_GREEN`, -- mafia
					`COMPONENT_CRIPSARP_CLIP_GREEN`, -- mafia
					`COMPONENT_BLOODSARP_CLIP_GREEN`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_white'] = {
			label = 'WT BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_BMARP_CLIP_WHITE`, -- mafia
					`COMPONENT_FAMILIASARP_CLIP_WHITE`, -- mafia
					`COMPONENT_CARTELARP_CLIP_WHITE`, -- mafia
					`COMPONENT_CRIPSARP_CLIP_WHITE`, -- mafia
					`COMPONENT_BLOODSARP_CLIP_WHITE`, -- mafia
				},
				usetime = 5500
			}
		},

		['at_clip_drum_pistol'] = {
			label = '50 Round Drum',
			type = 'magazine',
			weight = 500,
			client = {
				component = {
					`COMPONENT_GLOCK19X_CLIP_03`, -- v4
					`COMPONENT_GLOCK19_CLIP_03`, -- v4
					`COMPONENT_GLOCK40_CLIP_03`, -- v4
					`COMPONENT_GLOCK40S_CLIP_03`, -- v4
					`COMPONENT_GLOCK18C_CLIP_03`, -- v4
					`COMPONENT_ILLGLOCK17_CLIP_03`, -- v5
					`COMPONENT_BLUEGLOCKS_CLIP_03`, -- v5
					`COMPONENT_GLOCKBEAMS_CLIP_03`, -- v5
					`COMPONENT_MGGLOCK_CLIP_03`, -- v5
					`COMPONENT_TGLOCK19_CLIP_03`, -- v5
					`COMPONENT_MIDASGLOCK_CLIP_03`, -- v5
					`COMPONENT_G19XD_CLIP_DRUM`, -- v6
					`COMPONENT_GLOCK20S_CLIP_DRUM`, -- v6
					`COMPONENT_GLOCK22_CLIP_DRUM`, -- v6
					`COMPONENT_GLOCK22S_CLIP_DRUM`, -- v6
					`COMPONENT_GLOCK45_CLIP_DRUM`, -- v6
					`COMPONENT_ILLGLOCK19X_CLIP_DRUM`, -- v6
					`COMPONENT_TGLOCK45_CLIP_DRUM`, -- v6
                    `COMPONENT_CARTELGLOCK_CLIP_DRUM`, -- mafia [A]
					`COMPONENT_CRIPSGLOCK_CLIP_DRUM`, -- mafia [A]
					`COMPONENT_BLOODSGLOCK_CLIP_DRUM`, -- mafia [A]
					`COMPONENT_BMGLOCK_CLIP_DRUM`, -- mafia [A]
					`COMPONENT_FAMILIASGLOCK_CLIP_DRUM`, -- mafia [A]
					`COMPONENT_PLAYBOYGLOCK_CLIP_DRUM`, -- mafia [B]
					`COMPONENT_MOBGLOCK_CLIP_DRUM`, -- mafia [B]
					`COMPONENT_GRAPEGLOCK_CLIP_DRUM`, -- mafia [B]
					`COMPONENT_HOOVERGLOCK_CLIP_DRUM`, -- mafia [B]
					`COMPONENT_KINGZGLOCK_CLIP_DRUM`, -- mafia [B]
				},
				usetime = 2500
			}
		},

		['at_clip_100_yellow'] = {
			label = 'YEL 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_KINGZGLOCK_CLIP_YELLOW`, -- mafia [B]
					`COMPONENT_GRAPEGLOCK_CLIP_YELLOW`, -- mafia [B]
					`COMPONENT_HOOVERGLOCK_CLIP_YELLOW`, -- mafia [B]
					`COMPONENT_MOBGLOCK_CLIP_YELLOW`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_100_pink'] = {
			label = 'PINK 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_PLAYBOYGLOCK_CLIP_PINK`, -- mafia [B]
					`COMPONENT_GRAPEGLOCK_CLIP_PINK`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_100_brown'] = {
			label = 'BRN 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_MOBGLOCK_CLIP_BROWN`, -- mafia [B]
					`COMPONENT_KINGZGLOCK_CLIP_BROWN`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_100_orange'] = {
			label = 'ORG 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_MOBGLOCK_CLIP_ORANGE`, -- mafia [B]
					`COMPONENT_KINGZGLOCK_CLIP_ORANGE`, -- mafia [B]
					`COMPONENT_HOOVERGLOCK_CLIP_ORANGE`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_100_purple'] = {
			label = 'PUR 100R Drum',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_PLAYBOYGLOCK_CLIP_PURPLE`, -- mafia [B]
					`COMPONENT_GRAPEGLOCK_CLIP_PURPLE`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_pink'] = {
			label = 'PNK BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_PLAYBOYARP_CLIP_PINK`, -- mafia [B]
					`COMPONENT_MOBARP_CLIP_PINK`, -- mafia [B]
					`COMPONENT_GRAPEARP_CLIP_PINK`, -- mafia [B]
					`COMPONENT_HOOVERARP_CLIP_PINK`, -- mafia [B]
					`COMPONENT_LATINARP_CLIP_PINK`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_brown'] = {
			label = 'BRN BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_PLAYBOYARP_CLIP_BROWN`, -- mafia [B]
					`COMPONENT_MOBARP_CLIP_BROWN`, -- mafia [B]
					`COMPONENT_GRAPEARP_CLIP_BROWN`, -- mafia [B]
					`COMPONENT_HOOVERARP_CLIP_BROWN`, -- mafia [B]
					`COMPONENT_LATINARP_CLIP_BROWN`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_orange'] = {
			label = 'ORG BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_PLAYBOYARP_CLIP_ORANGE`, -- mafia [B]
					`COMPONENT_MOBARP_CLIP_ORANGE`, -- mafia [B]
					`COMPONENT_GRAPEARP_CLIP_ORANGE`, -- mafia [B]
					`COMPONENT_HOOVERARP_CLIP_ORANGE`, -- mafia [B]
					`COMPONENT_LATINARP_CLIP_ORANGE`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_yellow'] = {
			label = 'YEL BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_PLAYBOYARP_CLIP_YELLOW`, -- mafia [B]
					`COMPONENT_MOBARP_CLIP_YELLOW`, -- mafia [B]
					`COMPONENT_GRAPEARP_CLIP_YELLOW`, -- mafia [B]
					`COMPONENT_HOOVERARP_CLIP_YELLOW`, -- mafia [B]
					`COMPONENT_LATINARP_CLIP_YELLOW`, -- mafia [B]
				},
				usetime = 5500
			}
		},

		['at_clip_bandana_purple'] = {
			label = 'PUR BANDANA DRUM',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_BMARP_CLIP_PURPLE`, -- mafia [B]
					`COMPONENT_MOBARP_CLIP_PURPLE`, -- mafia [B]
					`COMPONENT_GRAPEARP_CLIP_PURPLE`, -- mafia [B]
					`COMPONENT_HOOVERARP_CLIP_PURPLE`, -- mafia [B]
					`COMPONENT_LATINARP_CLIP_PURPLE`, -- mafia [B]
				},
				usetime = 5500
			}
		},

        ['at_clip_extended_rifle'] = {
			label = 'Extended Rifle Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_ADVANCEDRIFLE_CLIP_02`,
					`COMPONENT_ASSAULTRIFLE_CLIP_02`,
					`COMPONENT_ASSAULTRIFLE_MK2_CLIP_02`,
					`COMPONENT_BULLPUPRIFLE_CLIP_02`,
					`COMPONENT_BULLPUPRIFLE_MK2_CLIP_02`,
					`COMPONENT_CARBINERIFLE_CLIP_02`,
					`COMPONENT_CARBINERIFLE_MK2_CLIP_02`,
					`COMPONENT_COMPACTRIFLE_CLIP_02`,
					`COMPONENT_HEAVYRIFLE_CLIP_02`,
					`COMPONENT_MILITARYRIFLE_CLIP_02`,
					`COMPONENT_SPECIALCARBINE_CLIP_02`,
					`COMPONENT_SPECIALCARBINE_MK2_CLIP_02`,
					`COMPONENT_TACTICALRIFLE_CLIP_02`,
					`COMPONENT_CARBINERIFLE_BOXMAG`,
					`COMPONENT_PDHK417_CLIP_02`, -- pd
					`COMPONENT_PINKSCAR_CLIP_02`, -- female 
					`COMPONENT_ACE_CLIP_02`, -- v2
					`COMPONENT_HERAARMS_BOXMAG`, -- v2
					`COMPONENT_G36K_CLIP_02`, -- v2
                    `COMPONENT_DDM4V7_CLIP_02`, -- v3
                    `COMPONENT_SCARV3_CLIP_02`, -- v3
                    `COMPONENT_AUGA1_CLIP_02`, -- v3
					`COMPONENT_BARP_CLIP_02`, -- v4
					`COMPONENT_PLR_CLIP_02`, -- v4   
					`COMPONENT_BSCAR_CLIP_02`, -- v5
					`COMPONENT_LBTANARP_CLIP_02`, -- v5
					`COMPONENT_RAM7_CLIP_02`, -- v5
					`COMPONENT_REDARP_CLIP_02`, -- v5
					`COMPONENT_REDM4A1_CLIP_02`, -- v5
					`COMPONENT_BLACKARP_CLIP_02`, -- v5
					`COMPONENT_TANARP_CLIP_02`, -- v5
					`COMPONENT_WOARP_CLIP_02`, -- v5
					`COMPONENT_BLUEARP_CLIP_EXTENDED`, -- v6
					`COMPONENT_M16_CLIP_02`, -- v6
					`COMPONENT_RAM7K_CLIP_02`, -- v6
					`COMPONENT_REDAUG_CLIP_EXTENDED`, -- v6
					`COMPONENT_CZBREN_CLIP_02`, -- v6
					`COMPONENT_MINIAK47_CLIP_02`, -- v6
                    `COMPONENT_PLAYBOYARP_CLIP_PINK`, -- MAFIA PACK
                    `COMPONENT_MOBARP_CLIP_BROWN`, -- MAFIA PACK
                    `COMPONENT_BMARP_CLIP_BLACK`, -- MAFIA PACK
                    `COMPONENT_LATINARP_CLIP_YELLOW`, -- MAFIA PACK
                    `COMPONENT_HOOVERARP_CLIP_ORANGE`, -- MAFIA PACK
                    `COMPONENT_GRAPEARP_CLIP_PURPLE`, -- MAFIA PACK
                    `COMPONENT_FAMILIASARP_CLIP_GREEN`, -- MAFIA PACK
                    `COMPONENT_CRIPSARP_CLIP_BLUE`, -- MAFIA PACK
                    `COMPONENT_CARTELARP_CLIP_WHITE`, -- MAFIA PACK
                    `COMPONENT_BLOODSARP_CLIP_RED`, -- MAFIA PACK
					`COMPONENT_SCARH_CLIP_02`, -- PD
					`COMPONENT_M4_CLIP_01`, -- PD
                    `w_ar_a15rc_mag2`,
					`w_ar_ak47s_mag2`,
					`w_ar_famasu1_mag2`,
					`w_ar_grau_mag2`,
					`w_ar_iar_mag2`,
					`w_ar_jrbak_mag2`,
					`w_ar_m133_mag2`,
					`w_ar_neva_mag2`,
					`w_ar_sr47_mag2`,
					`w_ar_ak4k_mag2`,
					`w_ar_akmkh_mag2`,
					`w_ar_bulldog_mag2`,
					`w_ar_casr_mag2`,
					`w_ar_drh_mag2`,
					`w_ar_fmr_mag2`,
					`w_ar_fn42_mag2`,
					`w_ar_galilar_mag2`,
					`w_ar_m16a3_mag2`,
					`w_ar_slr15_mag2`,
					`w_ar_arc15_mag2`,
					`w_ar_ars_mag2`,
					`w_ar_howa_2_mag2`,
					`w_ar_mza_mag2`,
					`w_ar_safak_mag2`,
					`w_ar_sar_mag2`,
					`w_ar_sfak_mag2`,
					`w_ar_arma1_mag2`,
					`w_ar_g36_mag2`,
					`w_ar_lr300_mag2`,
					`w_ar_m416p_mag2`,
					`w_ar_nanite_mag2`,
					`w_ar_sf2_mag2`,
					`w_ar_sfrifle_mag2`,
					`w_ar_ak47_mag2`,
					`w_ar_aug_mag2`,
					`w_ar_cfs_mag2`,
					`w_ar_g3_2_mag2`,
					`w_ar_groza_mag2`,
					`w_ar_sunda_mag2`,
                    `w_ar_scarh_mag2`,
				},
				usetime = 2500
			}
		},

		['at_scope_small'] = {
			label = 'Small Scope',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_SMALL`,
					`COMPONENT_AT_SCOPE_SMALL_02`,
					`COMPONENT_AT_SCOPE_SMALL_MK2`,
					`COMPONENT_AT_SCOPE_SMALL_SMG_MK2`,
					`COMPONENT_AT_PDG19G4_SCOPE_SMALL`, -- pd
                    `w_at_sb_sb4s_scope`,
					`w_at_sb_h2smg_scope`,
					`w_at_sb_hfsmg_scope`,
					`w_at_sb_ms32_scope`,
					`w_at_sb_sarb_scope`,
					`w_at_sb_ue4_scope`,
					`w_at_sb_idw_scope`,
					`w_at_sb_uzi_scope`,
				},
				usetime = 2500
			}
		},

		['at_scope_medium'] = {
			label = 'Medium Scope',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_MEDIUM`,
					`COMPONENT_AT_SCOPE_MEDIUM_MK2`,
					`COMPONENT_AT_PINKMK18_SCOPE_MEDIUM`, -- female
					`COMPONENT_AT_PDHK417_SCOPE_MEDIUM`, -- pd
					`COMPONENT_AT_SCOPE_HERAARMS_MEDIUM`, -- v2
					`COMPONENT_AT_MP9_SCOPE_MACRO`, -- v2
					`COMPONENT_AT_ARPISTOLSCOPE_MEDIUM`, -- v4
					`COMPONENT_AT_BAR15_SCOPE_MEDIUM`, -- v5
					`COMPONENT_AT_DMK18_SCOPE_MEDIUM`, -- v5
					`COMPONENT_SPEAR_SCOPE_MEDIUM`, -- v6
                    `w_at_ar_a15rc_scope`,
					`w_at_ar_ak47s_scope`,
					`w_at_ar_famasu1_scope`,
					`w_at_ar_grau_scope`,
					`w_at_ar_iar_scope`,
					`w_at_ar_jrbak_scope`,
					`w_at_ar_m133_scope`,
					`w_at_ar_neva_scope`,
					`w_at_ar_sr47_scope`,
					`w_at_ar_ak4k_scope`,
					`w_at_ar_akmkh_scope`,
					`w_at_ar_bulldog_scope`,
					`w_at_ar_casr_scope`,
					`w_at_ar_drh_scope`,
					`w_at_ar_fmr_scope`,
					`w_at_ar_fn42_scope`,
					`w_at_ar_galilar_scope`,
					`w_at_ar_m16a3_scope`,
					`w_at_ar_slr15_scope`,
					`w_at_ar_arc15_scope`,
					`w_at_ar_ars_scope`,
					`w_at_ar_howa_2_scope`,
					`w_at_ar_mza_scope`,
					`w_at_ar_safak_scope`,
					`w_at_ar_sar_scope`,
					`w_at_ar_sfak_scope`,
					`w_at_ar_arma1_scope`,
					`w_at_ar_g36_scope`,
					`w_at_ar_lr300_scope`,
					`w_at_ar_m416p_scope`,
					`w_at_ar_nanite_scope`,
					`w_at_ar_sf2_scope`,
					`w_at_ar_sfrifle_scope`,
					`w_at_ar_ak47_scope`,
					`w_at_ar_aug_scope`,
					`w_at_ar_cfs_scope`,
					`w_at_ar_g3_2_scope`,
					`w_at_ar_groza_scope`,
					`w_at_ar_sunda_scope`,
				},
				usetime = 2500
			}
		},

        -- V1 kyros
		['at_flashlight'] = {
			label = 'Tactical Flashlight',
			weight = 120,
			type = 'flashlight',
			client = {
				component = {
					`COMPONENT_AT_AR_FLSH`,
					`COMPONENT_AT_AR_FLSH_REH`,
					`COMPONENT_AT_PI_FLSH`,
					`COMPONENT_AT_PI_FLSH_02`,
					`COMPONENT_AT_PI_FLSH_03`,
				    `COMPONENT_AT_PI_PDG19G4_FLSH`, -- pd
					`COMPONENT_AT_AR_PDHK417_FLSH`,	-- pd
					`COMPONENT_AT_PI_PINKGLOCK19FLSH`, -- female
                    `COMPONENT_AT_G18C_FLSH`, -- v1
                    `COMPONENT_AT_AR_NSR_FLSH`, -- v1
					`COMPONENT_AT_AR_HERAARMS_FLSH`, -- v2
                    `COMPONENT_AT_PI_G2C_FLSH`, -- v3
                    `COMPONENT_AT_PI_PMR_FLSH`, -- v3
                    `COMPONENT_AT_PI_G19X_FLSH`, -- v3
                    `COMPONENT_AT_PI_G26_FLSH`, -- v3
					`COMPONENT_AT_AR_BARPFLSH`, -- v4
					`COMPONENT_AT_PI_RUGER57FLSH`, -- v4
					`COMPONENT_AT_AR_BAR15_FLSH`, -- v5
					`COMPONENT_AT_AR_DMK18_FLSH`, -- v5
					`COMPONENT_AT_PI_GLOCKBEAMS_FLSH`, -- v5 
					`COMPONENT_SPEAR_FLSH`, -- v6
					`COMPONENT_M9A3_FLSH`, -- v6
					`COMPONENT_SR9_FLSH`, -- v6
				},
				usetime = 2500
			}
		},

		['at_suppressor_light'] = {
			label = 'Suppressor',
			weight = 280,
			type = 'muzzle',
			client = {
				component = {
					`COMPONENT_AT_PI_SUPP`,
					`COMPONENT_AT_PI_SUPP_02`,
					`COMPONENT_CERAMICPISTOL_SUPP`,
					`COMPONENT_PISTOLXM3_SUPP`,
					`COMPONENT_AT_PI_PDG19G4_SUPP`, -- pd
					`COMPONENT_AT_AR_PINKPM9SUPP_02`, -- feamle
					`COMPONENT_AT_AR_PINKMK18_SUPP`, -- female
					`COMPONENT_AT_AR_NSR_SUPP`, -- v1
					`COMPONENT_AT_AR_HERAARMS_SUPP`, -- v2
					`COMPONENT_AT_AR_MAXIM9_SUPP`, -- v2
					`COMPONENT_AT_AR_HONEYBADGER_SUPP`, -- v2
					`COMPONENT_AT_AR_MP9_SUPP`, -- v2
					`COMPONENT_AT_PI_FN502V2_SUPP`, -- v2
					`COMPONENT_AT_AR_P416_SUPP`, -- v3
					`COMPONENT_AT_SR_P90_SUPP`, -- v3
					`COMPONENT_AT_AR_DMK18_SUPP`, -- v5
					`COMPONENT_AT_AR_REDM4A1_SUPP`, -- v5
					`COMPONENT_SPEAR_SUPP`, -- v6
					`COMPONENT_M9A3_SUPP`, -- v6
                    `w_at_sb_sb4s_supp`,
					`w_at_pi_fn502_supp`,
					`w_at_sb_h2smg_supp`,
					`w_at_sb_hfsmg_supp`,
					`w_at_sb_ms32_supp`,
					`w_at_sb_sarb_supp`,
					`w_at_sb_ue4_supp`,
					`w_at_sb_idw_supp`,
					`w_at_sb_uzi_supp`,
				},
				usetime = 2500
			}
		},

		['at_clip_extended_smg'] = {
			label = 'Extended SMG Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_ASSAULTSMG_CLIP_02`,
					`COMPONENT_COMBATPDW_CLIP_02`,
					`COMPONENT_MACHINEPISTOL_CLIP_02`,
					`COMPONENT_MICROSMG_CLIP_02`,
					`COMPONENT_MINISMG_CLIP_02`,
					`COMPONENT_SMG_CLIP_02`,
					`COMPONENT_SMG_MK2_CLIP_02`,
					`COMPONENT_PINKMP9_CLIP_02`, -- female
					`COMPONENT_MAC_CLIP_02`, -- v1
					`COMPONENT_TEC9_CLIP_02`, -- v1
					`CCOMPONENT_MAGPULPDR_CLIP_02`, -- v2
					`COMPONENT_MP9_CLIP_02`, -- v2
					`COMPONENT_TUZI_CLIP_02`, -- v3
					`COMPONENT_TEC9S_CLIP_02`, -- v5
					`COMPONENT_SUB2000_CLIP_02`, -- v6
					`COMPONENT_RAM9D_CLIP_02`, -- v6
                    `w_sb_sb4s_mag2`,
					`w_sb_h2smg_mag2`,
					`w_sb_hfsmg_mag2`,
					`w_sb_ms32_mag2`,
					`w_sb_sarb_mag2`,
					`w_sb_ue4_mag2`,
					`w_sb_idw_mag2`,
					`w_sb_uzi_mag2`,
				},
				usetime = 2500
			}
		},

        ['at_scope_large'] = {
			label = 'Large Scope',
			type = 'sight',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_SCOPE_LARGE_MK2`,
					`COMPONENT_AT_SCOPE_LARGE`,
					`COMPONENT_AT_BARRETT50_SCOPE_MAX`, -- v1
					`COMPONENT_AT_AWP_SCOPE_LARGE`, -- v3
					`COMPONENT_AT_M200_SCOPE_LARGE`, -- v3
					`COMPONENT_AT_AS50_SCOPE_LARGE`, -- v3
				},
				usetime = 2500
			}
		},

        -- V2 Kyros
		['at_grip'] = {
			label = 'Grip',
			type = 'grip',
			weight = 280,
			client = {
				component = {
					`COMPONENT_AT_AR_AFGRIP`,
					`COMPONENT_AT_AR_AFGRIP_02`,
					`COMPONENT_AT_AR_PINKMK18_AFGRIP`, -- female
					`COMPONENT_AT_AR_HERAARMS_AFGRIP`, -- v2
					`COMPONENT_AT_AR_LVOCA_AFGRIP`, -- v2
					`COMPONENT_AT_AR_LOKAFGRIP`, -- v4
					`COMPONENT_AT_AR_PLRAFGRIP`, -- v4
					`COMPONENT_AT_AR_BAR15_AFGRIP`, -- v5
					`COMPONENT_AT_AR_DMK18_AFGRIP`, -- v5
                    `COMPONENT_AT_AR_AFGRIP`,
                    `COMPONENT_AT_AR_AFGRIP_02`,
                    `w_at_ar_a15rc_grip`,
					`w_at_ar_ak47s_grip`,
					`w_at_ar_grau_grip`,
					`w_at_ar_iar_grip`,
					`w_at_ar_jrbak_grip`,
					`w_at_ar_m133_grip`,
					`w_at_ar_neva_grip`,
					`w_at_ar_sr47_grip`,
					`w_at_ar_ak4k_grip`,
					`w_at_ar_akmkh_grip`,
					`w_at_ar_casr_grip`,
					`w_at_ar_drh_grip`,
					`w_at_ar_fmr_grip`,
					`w_at_ar_galilar_grip`,
					`w_at_ar_m16a3_grip`,
					`w_at_ar_slr15_grip`,
					`w_at_ar_arc15_grip`,
					`w_at_ar_ars_grip`,
					`w_at_ar_howa_2_grip`,
					`w_at_ar_mza_grip`,
					`w_at_ar_safak_grip`,
					`w_at_ar_sfak_grip`,
					`w_at_ar_arma1_grip`,
					`w_at_ar_g36_grip`,
					`w_at_ar_lr300_grip`,
					`w_at_ar_m416p_grip`,
					`w_at_ar_nanite_grip`,
					`w_at_ar_sfrifle_grip`,
					`w_at_ar_ak47_grip`,
					`w_at_ar_cfs_grip`,
					`w_at_ar_g3_2_grip`,
				},
				usetime = 2500
			}
		},

		['at_clip_extended_shotgun'] = {
			label = 'Extended Shotgun Clip',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_ASSAULTSHOTGUN_CLIP_02`,
					`COMPONENT_HEAVYSHOTGUN_CLIP_02`,
					`COMPONENT_AA12_CLIP_02`, -- v2
				},
				usetime = 2500
			}
		},

        -- kyros v4
		['at_clip_100_pistol'] = {
			label = '100 Round Mag',
			type = 'magazine',
			weight = 800,
			client = {
				component = {
					`COMPONENT_GLOCK19X_CLIP_04`, -- v4
					`COMPONENT_GLOCK40_CLIP_04`, -- v4
					`COMPONENT_GLOCK40S_CLIP_04`, -- v4
					`COMPONENT_ILLGLOCK17_CLIP_05`, -- v5
					`COMPONENT_BLUEGLOCKS_CLIP_04`, -- v5
					`COMPONENT_GLOCKBEAMS_CLIP_04`, -- v5
					`COMPONENT_MGGLOCK_CLIP_04`, -- v5
					`COMPONENT_TGLOCK19_CLIP_04`, -- v5
					`COMPONENT_MIDASGLOCK_CLIP_04`, -- v5
					`COMPONENT_G19XD_CLIP_100`, -- v6
					`COMPONENT_GLOCK20S_CLIP_100`, -- v6
					`COMPONENT_GLOCK22_CLIP_100`, -- v6
					`COMPONENT_GLOCK22S_CLIP_100`, -- v6
					`COMPONENT_GLOCK45_CLIP_100`, -- v6
					`COMPONENT_ILLGLOCK19X_CLIP_100`, -- v6
					`COMPONENT_TGLOCK45_CLIP_100`, -- v6
				},
				usetime = 3500
			}
		},

        ['at_suppressor_heavy'] = {
            label = 'Tactical Suppressor',
            weight = 280,
            type = 'barrel',
            client = {
                component = {
                    `COMPONENT_AT_AR_SUPP`,
                    `COMPONENT_AT_AR_SUPP_02`,
                    `COMPONENT_AT_SR_SUPP`,
                    `COMPONENT_AT_SR_SUPP_03`,
                    `w_at_ar_a15rc_supp`,
                    `w_at_ar_ak47s_supp`,
                    `w_at_ar_famasu1_supp`,
                    `w_at_ar_grau_supp`,
                    `w_at_ar_iar_supp`,
                    `w_at_ar_jrbak_supp`,
                    `w_at_ar_m133_supp`,
                    `w_at_ar_neva_supp`,
                    `w_at_ar_sr47_supp`,
                    `w_at_ar_ak4k_supp`,
                    `w_at_ar_akmkh_supp`,
                    `w_at_ar_bulldog_supp`,
                    `w_at_ar_casr_supp`,
                    `w_at_ar_drh_supp`,
                    `w_at_ar_fmr_supp`,
                    `w_at_ar_fn42_supp`,
                    `w_at_ar_galilar_supp`,
                    `w_at_ar_m16a3_supp`,
                    `w_at_ar_slr15_supp`,
                    `w_at_ar_arc15_supp`,
                    `w_at_ar_ars_supp`,
                    `w_at_ar_howa_2_supp`,
                    `w_at_ar_mza_supp`,
                    `w_at_ar_safak_supp`,
                    `w_at_ar_sar_supp`,
                    `w_at_ar_sfak_supp`,
                    `w_at_ar_arma1_supp`,
                    `w_at_ar_g36_supp`,
                    `w_at_ar_lr300_supp`,
                    `w_at_ar_m416p_supp`,
                    `w_at_ar_nanite_supp`,
                    `w_at_ar_sf2_supp`,
                    `w_at_ar_sfrifle_supp`,
                    `w_at_ar_ak47_supp`,
                    `w_at_ar_aug_supp`,
                    `w_at_sr_awp_supp`,
                    `w_at_ar_cfs_supp`,
                    `w_at_sr_ditdg_supp`,
                    `w_at_ar_g3_2_supp`,
                    `w_at_ar_groza_supp`,
                    `w_at_ar_sunda_supp`,
                },
                usetime = 2500
            }
        },

        ['at_barrel'] = {
            label = 'Heavy Barrel',
            type = 'barrel',
            weight = 280,
            client = {
                component = {`COMPONENT_AT_SR_BARREL_01`,`COMPONENT_AT_MRFL_BARREL_02`,`COMPONENT_AT_MG_BARREL_02`,`COMPONENT_AT_SC_BARREL_02`, `COMPONENT_AT_CR_BARREL_02`, `COMPONENT_AT_BP_BARREL_02`},
                usetime = 2500
            }
        },

        ['at_clip_extended_mg'] = {
            label = 'Extended MG Clip',
            type = 'magazine',
            weight = 280,
            client = {
                component = {`COMPONENT_MG_CLIP_02`, `COMPONENT_COMBATMG_CLIP_02`, `COMPONENT_GUSENBERG_CLIP_02`, `COMPONENT_COMBATMG_MK2_CLIP_02`},
                usetime = 2500
            }
        },

        ['at_clip_extended_sniper'] = {
            label = 'Extended Sniper Clip',
            type = 'magazine',
            weight = 280,
            client = {
                component = {`COMPONENT_MARKSMANRIFLE_CLIP_02`, `COMPONENT_HEAVYSNIPER_MK2_CLIP_02`, `COMPONENT_MARKSMANRIFLE_MK2_CLIP_02`},
                usetime = 2500
            }
        },

        ['at_clip_drum_smg'] = {
            label = 'SMG Drum Clip',
            type = 'magazine',
            weight = 280,
            client = {
                component = {`COMPONENT_MACHINEPISTOL_CLIP_03`, `COMPONENT_SMG_CLIP_03`, `COMPONENT_COMBATPDW_CLIP_03`},
                usetime = 2500
            }
        },

        ['at_clip_drum_shotgun'] = {
            label = 'Shotgun Drum Clip',
            type = 'magazine',
            weight = 280,
            client = {
                component = {`COMPONENT_HEAVYSHOTGUN_CLIP_03`},
                usetime = 2500
            }
        },

		['at_clip_drum_rifle'] = {
			label = 'Rifle Drum',
			type = 'magazine',
			weight = 280,
			client = {
				component = {
					`COMPONENT_ASSAULTRIFLE_CLIP_03`,
					`COMPONENT_COMPACTRIFLE_CLIP_03`,
					`COMPONENT_CARBINERIFLE_CLIP_03`,
					`COMPONENT_SPECIALCARBINE_CLIP_03`,
					`COMPONENT_TANARP_CLIP_03`, -- v5
					`COMPONENT_LBTANARP_CLIP_03`, -- v5
					`COMPONENT_WOARP_CLIP_03`, -- v5
					`COMPONENT_REDARP_CLIP_03`, -- v5
					`COMPONENT_THOMPSON_CLIP_02`, -- v5
					`COMPONENT_BLUEARP_CLIP_DRUM`, -- v6
					`COMPONENT_MK47_CLIP_DRUM`, -- v6
				},
				usetime = 2500
			}
		},

        ['at_compensator'] = {
            label = 'Compensator',
            type = 'barrel',
            weight = 280,
            client = {
                component = {`COMPONENT_AT_PI_COMP_02`, `COMPONENT_AT_PI_COMP_03`, `COMPONENT_AT_PI_COMP`},
                usetime = 2500
            }
        },

        ['at_scope_advanced'] = {
            label = 'Advanced Scope',
            type = 'sight',
            weight = 280,
            client = {
                component = {`COMPONENT_AT_SCOPE_MAX`},
                usetime = 2500
            }
        },

        ['at_scope_zoom'] = {
            label = 'Zoom Scope',
            type = 'sight',
            weight = 280,
            client = {
                component = {`COMPONENT_AT_SCOPE_LARGE_MK2`},
                usetime = 2500
            }
        },

        ['at_scope_nv'] = {
            label = 'NV Scope',
            type = 'sight',
            weight = 420,
            client = {
                component = {`COMPONENT_AT_SCOPE_NV`},
                usetime = 2500
            }
        },

        ['at_scope_thermal'] = {
            label = 'Thermal Scope',
            type = 'sight',
            weight = 420,
            client = {
                component = {`COMPONENT_AT_SCOPE_THERMAL`},
                usetime = 2500
            }
        },

        ['at_muzzle_squared'] = {
            label = 'Squared Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_08`},
                usetime = 2500
            }
        },

        ['at_muzzle_bell'] = {
            label = 'Bell Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_09`},
                usetime = 2500
            }
        },

        ['at_muzzle_flat'] = {
            label = 'Flat Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_01`},
                usetime = 2500
            }
        },

        ['at_muzzle_tactical'] = {
            label = 'Tactical Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_02`},
                usetime = 2500
            }
        },

        ['at_muzzle_fat'] = {
            label = 'Fat Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_03`},
                usetime = 2500
            }
        },

        ['at_muzzle_precision'] = {
            label = 'Precision Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_04`},
                usetime = 2500
            }
        },

        ['at_muzzle_heavy'] = {
            label = 'Heavy Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_05`},
                usetime = 2500
            }
        },

        ['at_muzzle_slanted'] = {
            label = 'Slanted Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_06`},
                usetime = 2500
            }
        },

        ['at_muzzle_split'] = {
            label = 'Split Muzzle',
            type = 'barrel',
            weight = 80,
            client = {
                component = {`COMPONENT_AT_MUZZLE_07`},
                usetime = 2500
            }
        },

        ['at_skin_gold'] = {
            label = 'Luxury Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_MARKSMANRIFLE_VARMOD_LUXE`,`COMPONENT_MARKSMANRIFLE_VARMOD_LUXE`, `COMPONENT_SNIPERRIFLE_VARMOD_LUXE`,`COMPONENT_PUMPSHOTGUN_VARMOD_LOWRIDER`, `COMPONENT_SAWNOFFSHOTGUN_VARMOD_LUXE`,`COMPONENT_ASSAULTRIFLE_VARMOD_LUXE`, `COMPONENT_CARBINERIFLE_VARMOD_LUXE`, `COMPONENT_ADVANCEDRIFLE_VARMOD_LUXE`, `COMPONENT_SPECIALCARBINE_VARMOD_LOWRIDER`, `COMPONENT_BULLPUPRIFLE_VARMOD_LOW`, `COMPONENT_MG_VARMOD_LOWRIDER`,`COMPONENT_PISTOL_VARMOD_LUXE`, `COMPONENT_PISTOL50_VARMOD_LUXE`, `COMPONENT_APPISTOL_VARMOD_LUXE`, `COMPONENT_COMBATPISTOL_VARMOD_LOWRIDER`},
                usetime = 2500
            }
        },

        ['at_skin_camo'] = {
            label = 'Camo Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO`,`COMPONENT_COMBATMG_MK2_CAMO`,`COMPONENT_PUMPSHOTGUN_MK2_CAMO`,`COMPONENT_ASSAULTRIFLE_MK2_CAMO`, `COMPONENT_CARBINERIFLE_MK2_CAMO`, `COMPONENT_SPECIALCARBINE_MK2_CAMO`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO`, `COMPONENT_BULLPUPRIFLE_VARMOD_LOW`, `COMPONENT_MG_VARMOD_LOWRIDER`,`COMPONENT_SNSPISTOL_MK2_CAMO`, `COMPONENT_REVOLVER_MK2_CAMO`, `COMPONENT_PISTOL_MK2_CAMO`},
                usetime = 2500
            }
        },

        ['at_skin_brushstroke'] = {
            label = 'Brushstroke Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_02`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_02`,`COMPONENT_COMBATMG_MK2_CAMO_02`,`COMPONENT_PUMPSHOTGUN_MK2_CAMO_02`,`COMPONENT_ASSAULTRIFLE_MK2_CAMO_02`, `COMPONENT_CARBINERIFLE_MK2_CAMO_02`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_02`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_02`,`COMPONENT_PISTOL_MK2_CAMO_02`, `COMPONENT_REVOLVER_MK2_CAMO_02`, `COMPONENT_SNSPISTOL_MK2_CAMO_02`},
                usetime = 2500
            }
        },

        ['at_skin_woodland'] = {
            label = 'Woodland Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_03`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_03`,`COMPONENT_COMBATMG_MK2_CAMO_03`,`COMPONENT_PUMPSHOTGUN_MK2_CAMO_03`,`COMPONENT_ASSAULTRIFLE_MK2_CAMO_03`, `COMPONENT_CARBINERIFLE_MK2_CAMO_03`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_03`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_03`,`COMPONENT_PISTOL_MK2_CAMO_03`, `COMPONENT_REVOLVER_MK2_CAMO_03`, `COMPONENT_SNSPISTOL_MK2_CAMO_03`},
                usetime = 2500
            }
        },

        ['at_skin_skull'] = {
            label = 'Skull Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_04`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_04`, `COMPONENT_COMBATMG_MK2_CAMO_04`, `COMPONENT_PUMPSHOTGUN_MK2_CAMO_04`, `COMPONENT_ASSAULTRIFLE_MK2_CAMO_04`, `COMPONENT_CARBINERIFLE_MK2_CAMO_04`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_04`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_04`, `COMPONENT_PISTOL_MK2_CAMO_04`, `COMPONENT_REVOLVER_MK2_CAMO_04`, `COMPONENT_SNSPISTOL_MK2_CAMO_04`},
                usetime = 2500
            }
        },

        ['at_skin_sessanta'] = {
            label = 'Sessanta Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_05`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_05`, `COMPONENT_COMBATMG_MK2_CAMO_05`, `COMPONENT_PUMPSHOTGUN_MK2_CAMO_05`, `COMPONENT_ASSAULTRIFLE_MK2_CAMO_05`, `COMPONENT_CARBINERIFLE_MK2_CAMO_05`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_05`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_05`, `COMPONENT_PISTOL_MK2_CAMO_05`, `COMPONENT_REVOLVER_MK2_CAMO_05`, `COMPONENT_SNSPISTOL_MK2_CAMO_05`},
                usetime = 2500
            }
        },

        ['at_skin_perseus'] = {
            label = 'Perseus Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_06`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_06`, `COMPONENT_COMBATMG_MK2_CAMO_06`, `COMPONENT_PUMPSHOTGUN_MK2_CAMO_06`, `COMPONENT_ASSAULTRIFLE_MK2_CAMO_06`, `COMPONENT_CARBINERIFLE_MK2_CAMO_06`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_06`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_06`, `COMPONENT_PISTOL_MK2_CAMO_06`, `COMPONENT_REVOLVER_MK2_CAMO_06`, `COMPONENT_SNSPISTOL_MK2_CAMO_06`},
                usetime = 2500
            }
        },

        ['at_skin_leopard'] = {
            label = 'Leopard Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_07`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_07`, `COMPONENT_COMBATMG_MK2_CAMO_07`, `COMPONENT_PUMPSHOTGUN_MK2_CAMO_07`, `COMPONENT_ASSAULTRIFLE_MK2_CAMO_07`, `COMPONENT_CARBINERIFLE_MK2_CAMO_07`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_07`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_07`, `COMPONENT_PISTOL_MK2_CAMO_07`, `COMPONENT_REVOLVER_MK2_CAMO_07`, `COMPONENT_SNSPISTOL_MK2_CAMO_07`},
                usetime = 2500
            }
        },

        ['at_skin_zebra'] = {
            label = 'Zebra Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_08`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_08`,`COMPONENT_COMBATMG_MK2_CAMO_08`, `COMPONENT_PUMPSHOTGUN_MK2_CAMO_08`, `COMPONENT_ASSAULTRIFLE_MK2_CAMO_08`, `COMPONENT_CARBINERIFLE_MK2_CAMO_08`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_08`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_08`, `COMPONENT_PISTOL_MK2_CAMO_08`, `COMPONENT_REVOLVER_MK2_CAMO_08`, `COMPONENT_SNSPISTOL_MK2_CAMO_08`},
                usetime = 2500
            }
        },

        ['at_skin_geometric'] = {
            label = 'Geometric Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_09`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_09`, `COMPONENT_COMBATMG_MK2_CAMO_09`, `COMPONENT_PUMPSHOTGUN_MK2_CAMO_09`,`COMPONENT_ASSAULTRIFLE_MK2_CAMO_09`, `COMPONENT_CARBINERIFLE_MK2_CAMO_09`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_09`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_09`, `COMPONENT_PISTOL_MK2_CAMO_09`, `COMPONENT_REVOLVER_MK2_CAMO_09`, `COMPONENT_SNSPISTOL_MK2_CAMO_09`},
                usetime = 2500
            }
        },

        ['at_skin_boom'] = {
            label = 'Boom Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_10`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_10`, `COMPONENT_COMBATMG_MK2_CAMO_10`, `COMPONENT_PUMPSHOTGUN_MK2_CAMO_10`, `COMPONENT_ASSAULTRIFLE_MK2_CAMO_10`, `COMPONENT_CARBINERIFLE_MK2_CAMO_10`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_10`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_10`, `COMPONENT_PISTOL_MK2_CAMO_10`, `COMPONENT_REVOLVER_MK2_CAMO_10`, `COMPONENT_SNSPISTOL_MK2_CAMO_10`},
                usetime = 2500
            }
        },

        ['at_skin_patriotic'] = {
            label = 'Patriotic Weapon Kit',
            type = 'skin',
            weight = 50,
            client = {
                component = {`COMPONENT_HEAVYSNIPER_MK2_CAMO_IND_01`, `COMPONENT_MARKSMANRIFLE_MK2_CAMO_IND_01`, `COMPONENT_COMBATMG_MK2_CAMO_IND_01`, `COMPONENT_PUMPSHOTGUN_MK2_CAMO_IND_01`, `COMPONENT_ASSAULTRIFLE_MK2_CAMO_IND_01`, `COMPONENT_CARBINERIFLE_MK2_CAMO_IND_01`, `COMPONENT_SPECIALCARBINE_MK2_CAMO_IND_01`, `COMPONENT_BULLPUPRIFLE_MK2_CAMO_IND_01`, `COMPONENT_SNSPISTOL_MK2_CAMO_IND_01_SLIDE`, `COMPONENT_REVOLVER_MK2_CAMO_IND_01`, `COMPONENT_PISTOL_MK2_CAMO_IND_01`},
                usetime = 2500
            }
        },
    },

    Ammo = {

		['xd-ammo-musket'] = {
			label = '.50 Ball',
			weight = 38,
		},

		['xd_rifle_ammo'] = {
			label = 'Rifle Ammo',
			weight = 1,
		},

        ['xd-ammo-22'] = {
			label = 'Marksman Ammo',
			weight = 1,
		},

		['xd_pistol_ammo'] = {
			label = 'Pistol Ammo',
			weight = 1,
		},

        ['xd_rpg_ammo'] = {
			label = 'RPG Ammo',
			weight = 1,
		},

		['xd_smg_ammo'] = {
			label = 'SMG Ammo',
			weight = 1,
		},

		['xd_shotgun_ammo'] = {
			label = 'Shotgun Ammo',
			weight = 1,
		},

		['xd_sniper_ammo'] = {
			label = 'Sniper Ammo',
			weight = 5,
		},

        ['xd_gov_rifle_ammo'] = {
			label = 'Government Rifle Ammo',
			weight = 1,
		},

        ['xd_gov_pistol_ammo'] = {
			label = 'Government Pistol Ammo',
			weight = 1,
		},

        ['xd_gov_sniper_ammo'] = {
			label = 'Government Sniper Ammo',
			weight = 5,
		},

		['xd_gov_shotgun_ammo'] = {
			label = 'Government Shotgun Ammo',
			weight = 1,
		},

        ['xd_gov_smg_ammo'] = {
			label = 'Government SMG Ammo',
			weight = 1,
		},

	}
}

