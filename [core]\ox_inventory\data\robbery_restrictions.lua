-- Robbery Restrictions Configuration
-- Items listed here cannot be stolen/robbed from other players
-- You can add items to this list to prevent them from being taken during robberies

return {
    -- Protected items that cannot be robbed
    protected_items = {
        -- Personal/Identity Items
        'id_card',
        'driver_license',
        'weaponlicense',
        'lawyerpass',
        'phone',
        'radio',
        
        -- Keys and Access Items
        'carkey',
        'key',
        'oldkey',
        'keycard',
        'access_card',
        
        -- Important Documents
        'contract',
        'certificate',
        'passport',
        'visa',
        
        -- Medical Items (if you want to protect them)
        -- 'bandage',
        -- 'medikit',
        -- 'advancedkit',
        
        -- Tools (uncomment if you want to protect them)
        -- 'lockpick',
        -- 'drill',
        -- 'thermite_bomb',
        -- 'c4_bomb',
        
        -- Weapons (uncomment if you want to protect them)
        -- Note: You might want to allow weapon theft for realism
        -- 'WEAPON_PISTOL',
        -- 'WEAPON_SMG',
        
        -- Special Items
        'starter_packf',
        'starter_packm',
        
        -- Add more items here as needed
        -- Format: 'item_name',
    },
    
    -- Alternative: You can also define categories of items to protect
    -- This is for future expansion if you want to add category-based protection
    protected_categories = {
        -- 'weapons',
        -- 'medical',
        -- 'tools',
        -- 'documents',
    },
    
    -- Settings
    settings = {
        -- Whether to notify the robber when they try to take a protected item
        notify_robber = true,
        
        -- Whether to notify the victim when someone tries to take a protected item
        notify_victim = false,
        
        -- Custom notification messages
        messages = {
            robber_blocked = "You cannot steal this item.",
            victim_protected = "Someone tried to steal your %s but it was protected.",
        }
    }
}
