-- Robbery Restrictions Configuration
-- Items listed here cannot be stolen/robbed from other players
-- You can add items to this list to prevent them from being taken during robberies

return {
    -- Protected items that cannot be robbed
    protected_items = {
        -- Personal/Identity Items
        'id_card',
        'driver_license',
        'weaponlicense',
        'lawyerpass',
        'phone',
        'radio',
        
        -- Keys and Access Items
        'carkey',
        'key',
        'oldkey',
        'keycard',
        'access_card',

        'starter_packf',
        'starter_packm',
    },
    
    -- Alternative: You can also define categories of items to protect
    -- This is for future expansion if you want to add category-based protection
    protected_categories = {
        -- 'weapons',
    },
    
    settings = {
        notify_robber = true,
        notify_victim = false,
        
        messages = {
            robber_blocked = "You cannot steal this item.",
            victim_protected = "Someone tried to steal your %s but it was protected.",
        }
    }
}
